"use client";

import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./new-hero.module.scss";
import { useEffect, useMemo, useState } from "react";
import { Button, Image, rem, TextInput } from "@mantine/core";
import { IconSearch } from "@tabler/icons-react";
import { useMantineTheme } from "@mantine/core";
import ClockFast from "~/assets/icons/clock-fast.svg";
import { useLoaderData, useLocation, useNavigate } from "@remix-run/react";
import placeholderPerson from "~/assets/misc/placeholder-person.png";
import { useDebouncedState, useDisclosure } from "@mantine/hooks";
import { ComboboxItemPrice, DoctorProfile } from "~/types";
import {
  Spotlight,
  spotlight,
  SpotlightActionData,
  SpotlightActionGroupData,
} from "@mantine/spotlight";
import type { loader as pageLoader } from "~/routes/($lang)._index/route";
import { useTranslation } from "~/hooks";
import { APPOINTMENT_BOOKING_BASE_URL, TALK_TO_DOCTOR_ASAP_URL } from "~/constants";

export const NewHero = () => {
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const { state } = useLocation();
  const navigate = useNavigate();
  const { data } = useLoaderData<typeof pageLoader>();

  const [serviceType, setServiceType] = useState<ComboboxItemPrice | null>(
    state?.serviceType ?? null
  );
  const [search, setSearch] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useDebouncedState(
    search.toLocaleLowerCase(),
    400
  );
  const [doctors, setDoctors] = useState<DoctorProfile[]>(
    data?.profilesData ?? []
  );
  const [opened, { open, close }] = useDisclosure(false);
  const [language, setLanguage] = useState("all");
  const [yearsOfExperience, setYearsOfExperience] = useState("all");

  useEffect(() => {
    const originalDoctors = data?.profilesData ?? [];
    let filteredDoctors = [...originalDoctors];

    if (debouncedSearch) {
      filteredDoctors = filteredDoctors.filter(
        (doctor: DoctorProfile) =>
          doctor.name.toLocaleLowerCase().includes(debouncedSearch) ||
          doctor.specialties?.some(s =>
            s.toLocaleLowerCase().includes(debouncedSearch)
          )
      );
    }

    if (serviceType?.value) {
      filteredDoctors = filteredDoctors.filter(doctor =>
        doctor.services?.includes(serviceType.value)
      );
    }

    if (language !== "all") {
      filteredDoctors = filteredDoctors.filter(doctor =>
        doctor.languages?.includes(language.toLocaleLowerCase())
      );
    }

    if (yearsOfExperience !== "all") {
      filteredDoctors = filteredDoctors.filter(
        doctor => doctor.yearsOfExperience >= +yearsOfExperience
      );
    }

    setDoctors(filteredDoctors);
  }, [
    serviceType,
    debouncedSearch,
    data?.profilesData,
    language,
    yearsOfExperience,
  ]);

  const actions: (SpotlightActionData | SpotlightActionGroupData)[] = useMemo(
    () => [
      {
        group: t("doctors"),
        actions: doctors?.map((doctor: DoctorProfile) => ({
          id: doctor?.slug,
          label: doctor?.name,
          description: doctor?.specialization,
          onClick: () => {
            window.location.href = APPOINTMENT_BOOKING_BASE_URL + `/${doctor?.slug}?q=${doctor?.name}`;
          },
          leftSection: (
            <Image
              src={doctor?.image ?? placeholderPerson}
              alt={doctor?.name}
              h={30}
              w={30}
              width={30}
              height={30}
              radius={"100vmax"}
            />
          ),
        })),
      },
      {
        group: t("specialties"),
        actions:
          data?.specialtiesData &&
          Array.from(new Set<string>(data.specialtiesData))?.map(
            (specialty: string) => ({
              id: specialty,
              label: specialty,
              onClick: () => {
                window.location.href = APPOINTMENT_BOOKING_BASE_URL + `?q=${specialty}`;
              },
            })
          ),
      },
    ],
    [doctors, data?.specialties, t]
  );

  useEffect(() => {
    if (search) {
      setDebouncedSearch(search.toLocaleLowerCase());
    } else {
      setDebouncedSearch("");
    }
  }, [search, setDebouncedSearch]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value;
    setSearch(value);
  };

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <div className={classes["flex-content"]}>
          <div className={classes["mid-section"]}>
            <h1 className={classes.heading}>
              {t("connectTo")} <span>{t("qualityHealthcare")}</span>{" "}
              {t("inMinutes")}
            </h1>
            <p className={classes.description}>
              {t("empoweringYourHealthJourney")}
            </p>
            <div className={classes["search-div"]}>
              <form style={{ flex: 1 }}>
                <div style={{ display: "flex", justifyContent: "center" }}>
                  <div
                    style={{ cursor: "pointer" }}
                    onClick={() => spotlight.open()}
                    className={classes["search-input-div"]}>
                    <IconSearch size={18} />
                    <div style={{ flex: 1 }}>
                      <div className={classes["search-input"]}>
                        {t("searchPlaceholder")}
                      </div>
                    </div>
                    <div className={classes["inner-search-button"]}>
                      {t("searchButton")}
                    </div>
                  </div>
                </div>

                <Spotlight
                  actions={actions}
                  nothingFound={`${t("nothingFound")}...`}
                  clearQueryOnClose={false}
                  highlightQuery
                  yOffset={30}
                  fz={14}
                  limit={20}
                  searchProps={{
                    leftSection: (
                      <IconSearch
                        style={{ width: rem(20), height: rem(20) }}
                        stroke={1.5}
                      />
                    ),
                    placeholder: t("searchPlaceholder"),
                    inputSize: "sm",
                    size: "md",
                    fz: 14,
                    type: "search",
                    onKeyDown: e => {
                      if (e.key === "Enter") {
                        setSearch(e.currentTarget.value);
                        spotlight.close();
                      }
                    },
                  }}
                  scrollable
                  maxHeight={300}
                />
              </form>
            </div>

            <div className={classes["talk-div"]}>

              <a href={TALK_TO_DOCTOR_ASAP_URL} className={classes["talk-button"]}>
                <img
                  src={ClockFast}
                  alt="Clock Icon"
                  style={{ width: "1.5rem", height: "1.5rem" }}
                />
                {t("talkToDoctorImmediately")}
              </a>

              <a href={APPOINTMENT_BOOKING_BASE_URL} className={classes["book-test-button"]}>
                {t("book-lab-test")}
              </a>

            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
