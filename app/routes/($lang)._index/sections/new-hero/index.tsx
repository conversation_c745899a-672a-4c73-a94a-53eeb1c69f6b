"use client";

import utilityClasses from "~/styles/utilities.module.scss";
import classes from "./new-hero.module.scss";
import { useEffect, useState } from "react";
import { IconSearch } from "@tabler/icons-react";
import ClockFast from "~/assets/icons/clock-fast.svg";
import { useLoaderData, useLocation } from "@remix-run/react";
import placeholderPerson from "~/assets/misc/placeholder-person.png";
import { useDebouncedState } from "@mantine/hooks";
import { ComboboxItemPrice, DoctorProfile } from "~/types";
import type { loader as pageLoader } from "~/routes/($lang)._index/route";
import { useTranslation } from "~/hooks";
import {
  APPOINTMENT_BOOKING_BASE_URL,
} from "~/constants";
import { normalizeString } from "~/utils/normalize-string";


export const NewHero = () => {
  const { t, i18n } = useTranslation();
  const lang = i18n.language.startsWith("fr") ? "fr" : "en";
  const { state } = useLocation();
  const { data } = useLoaderData<typeof pageLoader>();

  const [serviceType, setServiceType] = useState<ComboboxItemPrice | null>(
    state?.serviceType ?? null
  );
  const [search, setSearch] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useDebouncedState("", 300);
  const [language, setLanguage] = useState("all");
  const [yearsOfExperience, setYearsOfExperience] = useState("all");

  const [filteredDoctors, setFilteredDoctors] = useState<DoctorProfile[]>([]);
  const [filteredSpecialties, setFilteredSpecialties] = useState<string[]>([]);

  const TALK_TO_DOCTOR_ASAP_URL = import.meta.env.VITE_APPOINTMENT_BASE_URL + `/${lang}/doctor/asap`;
  const BOOK_LAB_URL = import.meta.env.VITE_APPOINTMENT_BASE_URL + `/${lang}/lab`;

  useEffect(() => {
    const q = normalizeString(debouncedSearch.trim());

    if (!q) {
      setFilteredDoctors([]);
      setFilteredSpecialties([]);
      return;
    }

    const originalDoctors = data?.profilesData ?? [];
    let doctors = [...originalDoctors];

    if (serviceType?.value) {
      doctors = doctors.filter((d) => d.services?.includes(serviceType.value));
    }

    if (language !== "all") {
      doctors = doctors.filter((d) =>
        d.languages?.includes(language.toLowerCase())
      );
    }

    if (yearsOfExperience !== "all") {
      doctors = doctors.filter((d) => d.yearsOfExperience >= +yearsOfExperience);
    }

    const matchedDoctors = doctors.filter(
      (d) =>
        normalizeString(d.name).includes(q) ||
        d.specialties?.some((s: string) => normalizeString(s).includes(q))
    );

    setFilteredDoctors(matchedDoctors);

    const specialties = [...new Set(data?.specialtiesData ?? [])] as string[];
    const matchedSpecialties = specialties.filter((s) =>
      normalizeString(s).includes(q)
    );

    setFilteredSpecialties(matchedSpecialties);
  }, [
    debouncedSearch,
    data?.profilesData,
    data?.specialtiesData,
    serviceType,
    language,
    yearsOfExperience,
  ]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value;
    setSearch(value);
    setDebouncedSearch(normalizeString(value));
  };

  const handleDoctorClick = (doctor: DoctorProfile) => {
    window.location.href =
      APPOINTMENT_BOOKING_BASE_URL + `/${doctor.slug}?q=${doctor.name}`;
  };

  const handleSpecialtyClick = (specialty: string) => {
    window.location.href = APPOINTMENT_BOOKING_BASE_URL + `?q=${specialty}`;
  };

  return (
    <div className={classes.section}>
      <div className={utilityClasses.container}>
        <div className={classes["flex-content"]}>
          <div className={classes["mid-section"]}>
            <h1 className={classes.heading}>
              {t("connectTo")} <span>{t("qualityHealthcare")}</span>{" "}
              {t("inMinutes")}
            </h1>
            <p className={classes.description}>{t("empoweringYourHealthJourney")}</p>

            <div className={classes["search-div"]}>
              <div className={classes["search-wrapper"]} style={{ flex: 1, position: "relative" }}>
                <div
                  className={classes["search-input-div"]}
                  style={{
                    cursor: "auto",
                    borderBottomLeftRadius:
                      filteredDoctors.length > 0 || filteredSpecialties.length > 0
                        ? 0
                        : "30px",
                    borderBottomRightRadius:
                      filteredDoctors.length > 0 || filteredSpecialties.length > 0
                        ? 0
                        : "30px",
                  }}
                >
                  <IconSearch size={18} />
                  <div className={classes["search-input-container"]} style={{ flex: 1 }}>
                    <input
                      className={classes["search-input"]}
                      type="text"
                      placeholder={t("searchPlaceholder")}
                      value={search}
                      onChange={handleSearchChange}
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          if (e.currentTarget.value.trim()) {
                            window.location.href =
                              APPOINTMENT_BOOKING_BASE_URL + `?q=${e.currentTarget.value}`;
                          }
                        }
                      }}
                      style={{
                        border: "none",
                        outline: "none",
                        background: "transparent",
                        width: "100%",
                      }}
                    />
                  </div>
                  <button
                    className={classes["inner-search-button"]}
                    onClick={() => {
                      if (search.trim()) {
                        window.location.href =
                          APPOINTMENT_BOOKING_BASE_URL + `?q=${search}`;
                      }
                    }}
                  >
                    {t("searchButton")}
                  </button>
                </div>

                {(filteredDoctors.length > 0 || filteredSpecialties.length > 0) && (
                  <div
                    className={classes["search-results-div"]}
                    style={{
                      position: "absolute",
                      top: "100%",
                      left: 0,
                      right: 0,
                      background: "white",
                      border: "1px solid #EFEFEF",
                      borderTop: "none",
                      zIndex: 10,
                      maxHeight: "300px",
                      overflowY: "auto",
                    }}
                  >
                    {filteredDoctors.length > 0 && (
                      <div className={classes["search-results-group"]}>
                        <div className={classes["search-results-heading"]}>
                          {t("doctors")}
                        </div>
                        {filteredDoctors.map((doctor) => (
                          <div
                            key={doctor.slug}
                            className={classes["search-result-item"]}
                            onClick={() => handleDoctorClick(doctor)}
                          >
                            <img
                              src={doctor.image ?? placeholderPerson}
                              alt={doctor.name}
                              className={classes["search-result-image"]}
                            />
                            <div>
                              <div className={classes["search-result-name"]}>
                                {doctor.name}
                              </div>
                              <div className={classes["search-result-specialty"]}>
                                {doctor.specialization}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {filteredSpecialties.length > 0 && (
                      <div className={classes["search-results-group"]}>
                        <div className={classes["search-results-heading"]}>
                          {t("specialties")}
                        </div>
                        {filteredSpecialties.map((specialty) => (
                          <div
                            key={specialty}
                            className={classes["search-result-item"]}
                            onClick={() => handleSpecialtyClick(specialty)}
                          >
                            {specialty}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className={classes["talk-div"]}>
              <a
                href={TALK_TO_DOCTOR_ASAP_URL}
                className={classes["talk-button"]}
              >
                <img
                  src={ClockFast}
                  alt="Clock Icon"
                  style={{ width: "1.5rem", height: "1.5rem" }}
                />
                {t("talkToDoctorImmediately")}
              </a>

              <a href={BOOK_LAB_URL} className={classes["book-test-button"]}>
                {t("book-lab-test")}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
